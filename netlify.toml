# Netlify configuration for Next.js + Sanity project
[build]
  # Build command
  command = "npm run build"

  # Publish directory for Next.js
  publish = ".next"

  # Node.js version (using LTS version 20)
  environment = { NODE_VERSION = "20" }

[build.processing]
  # Skip processing of images, CSS, and JS as Next.js handles optimization
  skip_processing = false

[build.processing.css]
  bundle = true
  minify = true

[build.processing.js]
  bundle = true
  minify = true

[build.processing.html]
  pretty_urls = true

# Environment variables (these should also be set in Netlify dashboard)
[context.production.environment]
  NODE_ENV = "production"
  NEXT_TELEMETRY_DISABLED = "1"

[context.deploy-preview.environment]
  NODE_ENV = "production"
  NEXT_TELEMETRY_DISABLED = "1"

[context.branch-deploy.environment]
  NODE_ENV = "production"
  NEXT_TELEMETRY_DISABLED = "1"

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    # Security headers
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    X-XSS-Protection = "1; mode=block"
    Referrer-Policy = "strict-origin-when-cross-origin"

    # Performance headers
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "/*.html"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"

[[headers]]
  for = "/api/*"
  [headers.values]
    Cache-Control = "no-cache, no-store, must-revalidate"

[[headers]]
  for = "/studio/*"
  [headers.values]
    # Allow embedding for Sanity Studio
    X-Frame-Options = "SAMEORIGIN"
    Cache-Control = "no-cache, no-store, must-revalidate"

# Redirects for Sanity Studio (SPA routing)
[[redirects]]
  from = "/studio/*"
  to = "/studio/index.html"
  status = 200

# API routes are handled automatically by Netlify's Next.js runtime

# Remove the catch-all redirect - let Next.js handle routing
# Netlify's Next.js runtime will handle all page routing automatically