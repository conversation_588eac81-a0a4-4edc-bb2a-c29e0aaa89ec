# Nextjs 15 App Router + Sanity Starter Kit

## Features

- Next.js - This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app)
- Type checking [TypeScript](https://www.typescriptlang.org)
- Integrate with [Tailwind CSS v4](https://tailwindcss.com) and [Headless UI v2](https://headlessui.dev/)
- Linter with [ESLint](https://eslint.org)
- Code Formatter with [Prettier](https://prettier.io)
- [FontAwesome](https://fontawesome.com/icons) - Pro Solid and Regular icon libraries
- [Sanity](https://www.sanity.io/) - Content managment system
  - Integrated Sanity Studio embeded in the Next js project
  - Configured for Presentation Mode and Live editing
  - Configured for Sanity Create

### Requirements

[Node](https://nodejs.org/en/) 20.18.0 and npm
Recommend installing with [nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

```shell
nvm install
nvm use
```

## Getting Started

You'll need a Sanity account as well as a Sanity project To get started. Requires `.env` variables.

```
NEXT_PUBLIC_SANITY_PROJECT_ID=""
NEXT_PUBLIC_SANITY_DATASET="production"

SANITY_API_READ_TOKEN=

NEXT_PUBLIC_BASE_URL="https://nextjs-sanity-starter-kit.netlify.app"
```

<img src="public/images/preview/screenshot-sanity-dashboard.png" alt="Preview of Sanity Dashboard API Tab" width="600" />

You'll need a `projectId` and `apiToken` to get this up and running. Don't forget to add the CORS origin for localhost and the actual project URL. There is a `nextjs-sanity-starter-kit` Black Airplane Sanity project you can use if you want to test out the scaffold. Message or email [Herb](mailto:<EMAIL>) to get added to the project and get the `.env` file to get started quickly. If you spin up you're own project, remember to add `post` documents in Studio to test.

Then, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to launch local dev.  
Open [http://localhost:3000/studio](http://localhost:3000/studio) to open local Sanity Studio.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Inter](https://fonts.google.com/specimen/Inter).

## Sanity Create

Sanity create is another UI layer on top of Sanity Studio that actually maps out to the document of you're chosing. In this case, it's the `post` document. Sanity Create is still in Beta so there will be many changes as of March 5, 2025, when this repo was created. Sanity Create is avaiable to anyone with a Sanity account but you have to configure it to point to a project and document.

<img src="public/images/preview/screenshot-sanity-create.png" alt="Preview of Sanity Dashboard API Tab" width="600" />

Because we have integrated Sanity Studio in our Nextjs project, we won't rely on Sanity to host the studio but rather, do it oursleves. For Sanity Create to work, you need to deploy your project (Netlify, Vercel, etc...) and then configure Sanity Studio in the Sanity Admin Dashboard "Studio" tab. You'll want to add the custom url e.g., `https://your-website.com/studio`

<img src="public/images/preview/screenshot-sanity-studio-tab.png" alt="Preview of Sanity Dashboard API Tab" width="600" />

## Learn More

To learn more about Next.js and Sanity, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.
- [Sanity Visual Editing](https://www.sanity.io/guides/nextjs-app-router-live-preview) - Guide for Visual Editing using Next.js‘ App router.
- [Sanity Create](https://www.sanity.io/docs/create) - Introduction to Sanity Create.
- [Content Mapping for Sanity Create](https://www.sanity.io/docs/create-content-mapping) - Introduction to configuring Sanity Create in your project.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
