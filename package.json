{"name": "nextjs-sanity-starter-kit", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "debug": "npx @agentdeskai/browser-tools-mcp@1.1.0"}, "dependencies": {"@fortawesome/fontawesome-pro": "^6.7.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/pro-regular-svg-icons": "^6.7.2", "@fortawesome/pro-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@next/third-parties": "^15.1.6", "@sanity/icons": "^3.4.0", "@sanity/image-url": "^1.1.0", "@sanity/vision": "^3.77.1", "@tailwindcss/postcss": "^4.0.9", "date-fns": "^4.1.0", "next": "^15.1.7", "next-sanity": "^9.8.59", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.2.1", "react-player": "^2.16.0", "sanity": "^3.77.1", "styled-components": "^6.1.13"}, "devDependencies": {"@sanity/types": "^3.82.0", "@tailwindcss/cli": "^4.0.9", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20.17.6", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "dotenv": "^16.4.7", "eslint": "^8.57.1", "eslint-config-next": "15.0.1", "postcss": "^8.5.3", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.0.9", "tsx": "^4.19.3", "typescript": "^5.6.3"}}