/** @type {import('next').NextConfig} */
const nextConfig = {
  // Temporarily try static export if Next.js runtime doesn't work
  // output: 'export',
  // trailingSlash: true,

  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'cdn.sanity.io',
        pathname: '**',
      },
    ],
  },
  typescript: {
    // Set this to false if you want production builds to abort if there are type errors
    ignoreBuildErrors: process.env.NODE_ENV === 'development',
  },
  experimental: {
    esmExternals: 'loose', // Required for Sanity Studio
  },
}

export default nextConfig
